<?php get_header(); ?>

<!-- Slider main container -->
<div class="main-slider swiper">
    <?php
    $slider_query = new WP_Query(array(
        'post_type' => 'slider',
        'posts_per_page' => -1, // Tüm slider öğelerini göster
    ));

    if ($slider_query->have_posts()) :
        // Additional required wrapper
        echo '<div class="swiper-wrapper">';
        while ($slider_query->have_posts()) : $slider_query->the_post();
            // Slides
            echo '<div class="swiper-slide">';
            // Eğer öne çıkan görsel varsa (resim)
            if (has_post_thumbnail()) {
                the_post_thumbnail('full'); // Resmi tam boyutta göster
            }
            // Eğer içerik varsa (video embed kodu vb.)
            $content = get_the_content();
            if (!empty(trim($content))) {
                echo '<div class="video-container">';
                // İçeriği temizle ve gereksiz boşlukları kaldır
                $clean_content = trim($content);
                // Boş paragrafları ve gereksiz br etiketlerini kaldır
                $clean_content = preg_replace('/<p[^>]*>[\s&nbsp;]*<\/p>/', '', $clean_content);
                $clean_content = preg_replace('/^(<br\s*\/?>)+|(<br\s*\/?>)+$/', '', $clean_content);
                echo apply_filters('the_content', $clean_content);
                echo '</div>';
            }
            echo '</div>';
        endwhile;
        echo '</div>';

        // If we need pagination
        echo '<div class="swiper-pagination"></div>';

        // If we need navigation buttons
        echo '<div class="swiper-button-prev"></div>';
        echo '<div class="swiper-button-next"></div>';

        wp_reset_postdata();
    else :
        // Slider öğesi bulunamazsa gösterilecek mesaj
        echo '<p>Lütfen admin panelinden slider öğesi ekleyin.</p>';
    endif;
    ?>
</div>

<main>
    <div class="container">

        <?php if ( class_exists( 'WooCommerce' ) ) : ?>

        <!-- Populer Kurslar Bolumu -->
        <section class="homepage-section popular-courses">
            <div class="section-header">
                <h2 class="section-title">Populer Kurslar</h2>
                <p class="section-description">En cok satan kurslarimizi kesfet</p>
            </div>

            <div class="products-grid">
                <?php
                $popular_courses = dmrthema_get_popular_courses( 8 );

                if ( $popular_courses && $popular_courses->have_posts() ) :
                    woocommerce_product_loop_start();
                    while ( $popular_courses->have_posts() ) :
                        $popular_courses->the_post();
                        dmrthema_render_product_card( get_the_ID() );
                    endwhile;
                    woocommerce_product_loop_end();
                    wp_reset_postdata();
                else :
                    echo '<div class="no-products">';
                    echo '<h3>Henuz populer kurs bulunmuyor</h3>';
                    echo '<p>Yakinda harika kurslar eklenecek. Takipte kalin!</p>';
                    echo '</div>';
                endif;
                ?>
            </div>

            <?php if ( $popular_courses && $popular_courses->have_posts() ) : ?>
            <div class="section-footer">
                <a href="<?php echo esc_url( wc_get_page_permalink( 'shop' ) ); ?>" class="view-all-btn">
                    Tum Kursları Gor
                </a>
            </div>
            <?php endif; ?>
        </section>

        <!-- En Yuksek Puanli Urunler Bolumu -->
        <section class="homepage-section top-rated-products">
            <div class="section-header">
                <h2 class="section-title">En Yuksek Puanli Urunler</h2>
                <p class="section-description">Musteri memnuniyeti en yuksek urunler</p>
            </div>

            <div class="products-grid">
                <?php
                $top_rated_products = dmrthema_get_top_rated_products( 8 );

                if ( $top_rated_products && $top_rated_products->have_posts() ) :
                    woocommerce_product_loop_start();
                    while ( $top_rated_products->have_posts() ) :
                        $top_rated_products->the_post();
                        dmrthema_render_product_card( get_the_ID() );
                    endwhile;
                    woocommerce_product_loop_end();
                    wp_reset_postdata();
                else :
                    echo '<div class="no-products">';
                    echo '<h3>Henuz puanli urun bulunmuyor</h3>';
                    echo '<p>Musterilerimizin degerlendirmeleri bekleniyor.</p>';
                    echo '</div>';
                endif;
                ?>
            </div>

            <?php if ( $top_rated_products && $top_rated_products->have_posts() ) : ?>
            <div class="section-footer">
                <a href="<?php echo esc_url( wc_get_page_permalink( 'shop' ) ); ?>" class="view-all-btn">
                    Tum Urunleri Gor
                </a>
            </div>
            <?php endif; ?>
        </section>

        <?php endif; ?>

    </div>
</main>

<?php get_footer(); ?>
